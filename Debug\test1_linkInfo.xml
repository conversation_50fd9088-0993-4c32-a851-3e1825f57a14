<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o test1.out -mtest1.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/Desktop/001 -iC:/Users/<USER>/Desktop/001/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=test1_linkInfo.xml --rom_model ./app/Scheduler.o ./bsp/systick.o ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./app/Ganway.o ./app/Ganway_Optimized.o ./app/No_Mcu_Ganv_Grayscale_Sensor.o ./app/Square_Example.o ./app/Square_Track_Example.o ./app/Square_Track_Simple.o ./app/Track_Example.o ./app/encoder.o ./app/key.o ./app/motor.o ./app/ringbuffer.o ./app/OLED/oled.o ./bsp/bsp_usart.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688de931</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\Desktop\001\Debug\test1.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x28d5</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Scheduler.o</file>
         <name>Scheduler.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\bsp\</path>
         <kind>object</kind>
         <file>systick.o</file>
         <name>systick.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Ganway.o</file>
         <name>Ganway.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Ganway_Optimized.o</file>
         <name>Ganway_Optimized.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>No_Mcu_Ganv_Grayscale_Sensor.o</file>
         <name>No_Mcu_Ganv_Grayscale_Sensor.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Square_Example.o</file>
         <name>Square_Example.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Square_Track_Example.o</file>
         <name>Square_Track_Example.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Square_Track_Simple.o</file>
         <name>Square_Track_Simple.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>Track_Example.o</file>
         <name>Track_Example.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\</path>
         <kind>object</kind>
         <file>ringbuffer.o</file>
         <name>ringbuffer.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\app\OLED\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\Desktop\001\Debug\.\bsp\</path>
         <kind>object</kind>
         <file>bsp_usart.o</file>
         <name>bsp_usart.o</name>
      </input_file>
      <input_file id="fl-1f">
         <path>C:\Users\<USER>\Desktop\001\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-20">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-3c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>printf.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcpy.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strncpy.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsidf.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.OLED_ShowChar</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x1d0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x290</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x290</run_address>
         <size>0x194</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x424</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x5b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.No_MCU_Ganv_Sensor_Init</name>
         <load_address>0x5b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b8</run_address>
         <size>0x188</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.Set_PWM</name>
         <load_address>0x740</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x740</run_address>
         <size>0x16c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.Key_Scan_Debounce</name>
         <load_address>0x8ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8ac</run_address>
         <size>0x15c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text.main</name>
         <load_address>0xa08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa08</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0xb3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb3c</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.__divdf3</name>
         <load_address>0xc5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc5c</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xd68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd68</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.TIMG0_IRQHandler</name>
         <load_address>0xe6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe6c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.Square_Analyze_State</name>
         <load_address>0xf6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf6c</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x1064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1064</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.text.__muldf3</name>
         <load_address>0x114c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x114c</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.OLED_ShowNum</name>
         <load_address>0x1230</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1230</run_address>
         <size>0xe2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.OLED_Init</name>
         <load_address>0x1312</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1312</run_address>
         <size>0xde</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x13f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13f0</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.Get_Analog_value</name>
         <load_address>0x14cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14cc</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.normalizeAnalogValues</name>
         <load_address>0x159c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x159c</run_address>
         <size>0xaa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.Motor_Speed_Monitor</name>
         <load_address>0x1648</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1648</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.OLED_ShowSignedNum</name>
         <load_address>0x16f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16f0</run_address>
         <size>0x9a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.OLED_ShowString</name>
         <load_address>0x178a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x178a</run_address>
         <size>0x9a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.OLED_DrawPoint</name>
         <load_address>0x1824</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1824</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0x18b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b4</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1940</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1940</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.OLED_Refresh</name>
         <load_address>0x19cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19cc</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x1a50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a50</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1ad4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad4</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-21e">
         <name>.text.__gedf2</name>
         <load_address>0x1b50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b50</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.No_MCU_Ganv_Sensor_Init_Frist</name>
         <load_address>0x1bc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bc4</run_address>
         <size>0x72</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x1c38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c38</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.convertAnalogToDigital</name>
         <load_address>0x1ca4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ca4</run_address>
         <size>0x6c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text.Key_1</name>
         <load_address>0x1d10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d10</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-218">
         <name>.text.__ledf2</name>
         <load_address>0x1d78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d78</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1ec">
         <name>.text.__aeabi_dcmp</name>
         <load_address>0x1de0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1de0</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.OLED_Clear</name>
         <load_address>0x1e44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e44</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.Square_Track_Control</name>
         <load_address>0x1ea4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea4</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x1f04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f04</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x1f64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f64</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1fbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fbc</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_I2C_startControllerTransfer</name>
         <load_address>0x2010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2010</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.text.SysTick_Config</name>
         <load_address>0x2060</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2060</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0x20b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x20fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20fc</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x2148</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2148</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1e8">
         <name>.text.__fixdfsi</name>
         <load_address>0x2194</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2194</run_address>
         <size>0x4a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.text.adc_getValue</name>
         <load_address>0x21de</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21de</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.text.DL_UART_init</name>
         <load_address>0x2228</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2228</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.OLED_DisplayTurn</name>
         <load_address>0x2270</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2270</run_address>
         <size>0x48</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x22b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22b8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x2300</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2300</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x2348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2348</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.No_Mcu_Ganv_Sensor_Task_Without_tick</name>
         <load_address>0x238c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x238c</run_address>
         <size>0x42</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x23d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23d0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.Key</name>
         <load_address>0x2410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2410</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x2450</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2450</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x2490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2490</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0x24d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24d0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x250c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x250c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.Get_Anolog_Value</name>
         <load_address>0x2548</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2548</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x2584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2584</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-212">
         <name>.text.__muldsi3</name>
         <load_address>0x25c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25c0</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x25fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25fc</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.OLED_ColorTurn</name>
         <load_address>0x2630</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2630</run_address>
         <size>0x34</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x2664</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2664</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x2698</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2698</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-210">
         <name>.text.DL_ADC12_getMemResult</name>
         <load_address>0x26cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26cc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.OLED_Pow</name>
         <load_address>0x26fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26fc</run_address>
         <size>0x30</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x272c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x272c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x275c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x275c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x2788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2788</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.text.__floatsidf</name>
         <load_address>0x27b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x27b4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.strncpy</name>
         <load_address>0x27e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x27e0</run_address>
         <size>0x2c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x280c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x280c</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x2834</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2834</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-204">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x285c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x285c</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0x2884</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2884</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0x28ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x28ac</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x28d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x28d4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0x28fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x28fc</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x2922</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2922</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.__floatunsidf</name>
         <load_address>0x2948</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2948</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-197">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x296c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x296c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0x298c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x298c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.delay_ms</name>
         <load_address>0x29ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x29ac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x29cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x29cc</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x29ea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x29ea</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20c">
         <name>.text.DL_ADC12_startConversion</name>
         <load_address>0x2a08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a08</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-20e">
         <name>.text.DL_ADC12_stopConversion</name>
         <load_address>0x2a24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a24</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2a40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a40</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2a5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a5c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-196">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0x2a78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a78</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x2a94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a94</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-193">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x2ab0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ab0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x2acc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2acc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x2ae8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ae8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x2b04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b04</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x2b20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b20</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x2b3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b3c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x2b58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b58</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.Square_Track_Init</name>
         <load_address>0x2b74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b74</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x2b90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b90</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x2ba8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ba8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0x2bc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2bc0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x2bd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2bd8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x2bf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2bf0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x2c08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c08</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-198">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x2c20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x2c38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2c50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2c68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2c80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x2c98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c98</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.DL_GPIO_togglePins</name>
         <load_address>0x2cb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2cb0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0x2cc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2cc8</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0x2ce0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ce0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0x2cf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2cf8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0x2d10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d10</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x2d28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d28</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0x2d40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d40</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x2d58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d58</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x2d70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d70</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x2d88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d88</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x2da0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2da0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x2db8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2db8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x2dd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2dd0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_UART_reset</name>
         <load_address>0x2de8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2de8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20f">
         <name>.text.DL_ADC12_disableConversions</name>
         <load_address>0x2e00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e00</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-20b">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x2e16</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e16</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x2e2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e2c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x2e42</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e42</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x2e58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e58</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.text.DL_UART_enable</name>
         <load_address>0x2e6e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e6e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2e84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e84</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2e98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e98</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2eac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2eac</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2ec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ec0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_I2C_getControllerStatus</name>
         <load_address>0x2ed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ed4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0x2ee8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ee8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x2efc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2efc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2f10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f10</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x2f24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f24</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2f38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f38</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.Key_Init_Debounce</name>
         <load_address>0x2f4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f4c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.Square_Go_Straight</name>
         <load_address>0x2f60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f60</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.Square_Turn_Left</name>
         <load_address>0x2f74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f74</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-162">
         <name>.text.Square_Turn_Right</name>
         <load_address>0x2f88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f88</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x2f9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f9c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x2fae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2fae</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2fc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2fc0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x2fd2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2fd2</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x2fe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2fe4</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-20d">
         <name>.text.DL_ADC12_getStatus</name>
         <load_address>0x2ff6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ff6</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x3008</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3008</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x3018</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3018</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.Key_System_Tick_Inc</name>
         <load_address>0x3028</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3028</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text:decompress:ZI</name>
         <load_address>0x3038</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3038</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.Get_Digtal_For_User</name>
         <load_address>0x3048</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3048</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-163">
         <name>.text.Square_Handle_Lost</name>
         <load_address>0x3056</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3056</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text:TI_memset_small</name>
         <load_address>0x3064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3064</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x3072</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3072</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x3080</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3080</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text.get_systicks</name>
         <load_address>0x308c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x308c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.scheduler_init</name>
         <load_address>0x3098</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3098</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x30a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30a4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-53">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x30b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30b0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text:abort</name>
         <load_address>0x30b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30b8</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x30be</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30be</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-164">
         <name>.text.HOSTexit</name>
         <load_address>0x30c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30c2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x30c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30c6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text._system_pre_init</name>
         <load_address>0x30ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30ca</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-264">
         <name>.cinit..data.load</name>
         <load_address>0x4b28</load_address>
         <readonly>true</readonly>
         <run_address>0x4b28</run_address>
         <size>0x3a</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-262">
         <name>__TI_handler_table</name>
         <load_address>0x4b64</load_address>
         <readonly>true</readonly>
         <run_address>0x4b64</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-265">
         <name>.cinit..bss.load</name>
         <load_address>0x4b70</load_address>
         <readonly>true</readonly>
         <run_address>0x4b70</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-263">
         <name>__TI_cinit_table</name>
         <load_address>0x4b78</load_address>
         <readonly>true</readonly>
         <run_address>0x4b78</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.asc2_2412</name>
         <load_address>0x30d0</load_address>
         <readonly>true</readonly>
         <run_address>0x30d0</run_address>
         <size>0xd5c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.asc2_1608</name>
         <load_address>0x3e2c</load_address>
         <readonly>true</readonly>
         <run_address>0x3e2c</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.rodata.asc2_1206</name>
         <load_address>0x441c</load_address>
         <readonly>true</readonly>
         <run_address>0x441c</run_address>
         <size>0x474</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.rodata.asc2_0806</name>
         <load_address>0x4890</load_address>
         <readonly>true</readonly>
         <run_address>0x4890</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x4ab8</load_address>
         <readonly>true</readonly>
         <run_address>0x4ab8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x4ae0</load_address>
         <readonly>true</readonly>
         <run_address>0x4ae0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x4af4</load_address>
         <readonly>true</readonly>
         <run_address>0x4af4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x4afe</load_address>
         <readonly>true</readonly>
         <run_address>0x4afe</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x4b00</load_address>
         <readonly>true</readonly>
         <run_address>0x4b00</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0x4b08</load_address>
         <readonly>true</readonly>
         <run_address>0x4b08</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.rodata.str1.5850567729483738290.1</name>
         <load_address>0x4b10</load_address>
         <readonly>true</readonly>
         <run_address>0x4b10</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0x4b18</load_address>
         <readonly>true</readonly>
         <run_address>0x4b18</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x4b1b</load_address>
         <readonly>true</readonly>
         <run_address>0x4b1b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x4b1e</load_address>
         <readonly>true</readonly>
         <run_address>0x4b1e</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x4b21</load_address>
         <readonly>true</readonly>
         <run_address>0x4b21</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-22c">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6b">
         <name>.data.delay_times</name>
         <load_address>0x20200794</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200794</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.data.systicks</name>
         <load_address>0x20200788</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200788</run_address>
         <size>0x8</size>
         <alignment>0x8</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.data.Anolog</name>
         <load_address>0x20200738</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200738</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.data.black</name>
         <load_address>0x20200748</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200748</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.data.white</name>
         <load_address>0x20200768</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200768</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.data.rx_buff</name>
         <load_address>0x20200560</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200560</run_address>
         <size>0x100</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-96">
         <name>.data.D_Num</name>
         <load_address>0x20200784</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200784</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-99">
         <name>.data.Run</name>
         <load_address>0x20200790</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200790</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.data.track_ctrl</name>
         <load_address>0x20200718</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200718</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-159">
         <name>.data.square_ctrl</name>
         <load_address>0x20200758</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200758</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.data.key1_ctrl</name>
         <load_address>0x20200778</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200778</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.data.system_tick_ms</name>
         <load_address>0x20200798</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200798</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.data.speed_monitor</name>
         <load_address>0x202006e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202006e0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.data.uart_rx_buffer</name>
         <load_address>0x20200660</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200660</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.data.uart_rx_index</name>
         <load_address>0x2020079c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020079c</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.data.uart_rx_ticks</name>
         <load_address>0x2020079d</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020079d</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020055c</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-95">
         <name>.common:encoderB_cnt</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200550</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-94">
         <name>.common:encoderA_cnt</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020054c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-97">
         <name>.common:Flag_stop</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020053c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-98">
         <name>.common:Flag_stop1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200540</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-143">
         <name>.common:gPWM_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200480</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-71">
         <name>.common:gpio_interrup1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200554</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-72">
         <name>.common:gpio_interrup2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200558</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-73">
         <name>.common:Get_Encoder_countA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200544</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-74">
         <name>.common:Get_Encoder_countB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200548</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-bf">
         <name>.common:OLED_GRAM</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-267">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_abbrev</name>
         <load_address>0xf4</load_address>
         <run_address>0xf4</run_address>
         <size>0x143</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_abbrev</name>
         <load_address>0x237</load_address>
         <run_address>0x237</run_address>
         <size>0x1bb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_abbrev</name>
         <load_address>0x3f2</load_address>
         <run_address>0x3f2</run_address>
         <size>0x214</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0x606</load_address>
         <run_address>0x606</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_abbrev</name>
         <load_address>0x673</load_address>
         <run_address>0x673</run_address>
         <size>0x174</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_abbrev</name>
         <load_address>0x7e7</load_address>
         <run_address>0x7e7</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_abbrev</name>
         <load_address>0x954</load_address>
         <run_address>0x954</run_address>
         <size>0xec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_abbrev</name>
         <load_address>0xa40</load_address>
         <run_address>0xa40</run_address>
         <size>0x10b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_abbrev</name>
         <load_address>0xb4b</load_address>
         <run_address>0xb4b</run_address>
         <size>0x16a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0xcb5</load_address>
         <run_address>0xcb5</run_address>
         <size>0x191</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0xe46</load_address>
         <run_address>0xe46</run_address>
         <size>0x1b4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_abbrev</name>
         <load_address>0xffa</load_address>
         <run_address>0xffa</run_address>
         <size>0x1b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-209">
         <name>.debug_abbrev</name>
         <load_address>0x11b3</load_address>
         <run_address>0x11b3</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-201">
         <name>.debug_abbrev</name>
         <load_address>0x1324</load_address>
         <run_address>0x1324</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_abbrev</name>
         <load_address>0x1386</load_address>
         <run_address>0x1386</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_abbrev</name>
         <load_address>0x156d</load_address>
         <run_address>0x156d</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-207">
         <name>.debug_abbrev</name>
         <load_address>0x17f3</load_address>
         <run_address>0x17f3</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-205">
         <name>.debug_abbrev</name>
         <load_address>0x1a8e</load_address>
         <run_address>0x1a8e</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_abbrev</name>
         <load_address>0x1ca6</load_address>
         <run_address>0x1ca6</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_abbrev</name>
         <load_address>0x1d28</load_address>
         <run_address>0x1d28</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_abbrev</name>
         <load_address>0x1dd7</load_address>
         <run_address>0x1dd7</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_abbrev</name>
         <load_address>0x1f47</load_address>
         <run_address>0x1f47</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_abbrev</name>
         <load_address>0x1f80</load_address>
         <run_address>0x1f80</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0x2042</load_address>
         <run_address>0x2042</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_abbrev</name>
         <load_address>0x20b2</load_address>
         <run_address>0x20b2</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-168">
         <name>.debug_abbrev</name>
         <load_address>0x213f</load_address>
         <run_address>0x213f</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-1f3">
         <name>.debug_abbrev</name>
         <load_address>0x21d7</load_address>
         <run_address>0x21d7</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-1f1">
         <name>.debug_abbrev</name>
         <load_address>0x2203</load_address>
         <run_address>0x2203</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-216">
         <name>.debug_abbrev</name>
         <load_address>0x222a</load_address>
         <run_address>0x222a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-220">
         <name>.debug_abbrev</name>
         <load_address>0x2251</load_address>
         <run_address>0x2251</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-1f2">
         <name>.debug_abbrev</name>
         <load_address>0x2278</load_address>
         <run_address>0x2278</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-217">
         <name>.debug_abbrev</name>
         <load_address>0x229f</load_address>
         <run_address>0x229f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-211">
         <name>.debug_abbrev</name>
         <load_address>0x22c6</load_address>
         <run_address>0x22c6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1f0">
         <name>.debug_abbrev</name>
         <load_address>0x22ed</load_address>
         <run_address>0x22ed</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-21f">
         <name>.debug_abbrev</name>
         <load_address>0x2314</load_address>
         <run_address>0x2314</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_abbrev</name>
         <load_address>0x233b</load_address>
         <run_address>0x233b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_abbrev</name>
         <load_address>0x2362</load_address>
         <run_address>0x2362</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_abbrev</name>
         <load_address>0x2387</load_address>
         <run_address>0x2387</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-221">
         <name>.debug_abbrev</name>
         <load_address>0x23ae</load_address>
         <run_address>0x23ae</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1fe">
         <name>.debug_abbrev</name>
         <load_address>0x2476</load_address>
         <run_address>0x2476</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_abbrev</name>
         <load_address>0x24cf</load_address>
         <run_address>0x24cf</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_abbrev</name>
         <load_address>0x24f4</load_address>
         <run_address>0x24f4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-269">
         <name>.debug_abbrev</name>
         <load_address>0x2519</load_address>
         <run_address>0x2519</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x13c</load_address>
         <run_address>0x13c</run_address>
         <size>0x7df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0x91b</load_address>
         <run_address>0x91b</run_address>
         <size>0x19bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0x22d7</load_address>
         <run_address>0x22d7</run_address>
         <size>0x40cb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x63a2</load_address>
         <run_address>0x63a2</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_info</name>
         <load_address>0x6422</load_address>
         <run_address>0x6422</run_address>
         <size>0x7e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_info</name>
         <load_address>0x6c03</load_address>
         <run_address>0x6c03</run_address>
         <size>0x11cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0x7dcf</load_address>
         <run_address>0x7dcf</run_address>
         <size>0x222</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_info</name>
         <load_address>0x7ff1</load_address>
         <run_address>0x7ff1</run_address>
         <size>0x805</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_info</name>
         <load_address>0x87f6</load_address>
         <run_address>0x87f6</run_address>
         <size>0x8df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_info</name>
         <load_address>0x90d5</load_address>
         <run_address>0x90d5</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0xa32e</load_address>
         <run_address>0xa32e</run_address>
         <size>0x12b7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_info</name>
         <load_address>0xb5e5</load_address>
         <run_address>0xb5e5</run_address>
         <size>0xb1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.debug_info</name>
         <load_address>0xc100</load_address>
         <run_address>0xc100</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_info</name>
         <load_address>0xc845</load_address>
         <run_address>0xc845</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_info</name>
         <load_address>0xc8ba</load_address>
         <run_address>0xc8ba</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0xd57c</load_address>
         <run_address>0xd57c</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.debug_info</name>
         <load_address>0x106ee</load_address>
         <run_address>0x106ee</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.debug_info</name>
         <load_address>0x11994</load_address>
         <run_address>0x11994</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_info</name>
         <load_address>0x12a24</load_address>
         <run_address>0x12a24</run_address>
         <size>0xcd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x12af1</load_address>
         <run_address>0x12af1</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_info</name>
         <load_address>0x12f14</load_address>
         <run_address>0x12f14</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_info</name>
         <load_address>0x13658</load_address>
         <run_address>0x13658</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_info</name>
         <load_address>0x1369e</load_address>
         <run_address>0x1369e</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x13830</load_address>
         <run_address>0x13830</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x138f6</load_address>
         <run_address>0x138f6</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_info</name>
         <load_address>0x13a72</load_address>
         <run_address>0x13a72</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_info</name>
         <load_address>0x13b6a</load_address>
         <run_address>0x13b6a</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-152">
         <name>.debug_info</name>
         <load_address>0x13ba5</load_address>
         <run_address>0x13ba5</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.debug_info</name>
         <load_address>0x13d4c</load_address>
         <run_address>0x13d4c</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-213">
         <name>.debug_info</name>
         <load_address>0x13ed9</load_address>
         <run_address>0x13ed9</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_info</name>
         <load_address>0x14068</load_address>
         <run_address>0x14068</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1ea">
         <name>.debug_info</name>
         <load_address>0x141f5</load_address>
         <run_address>0x141f5</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.debug_info</name>
         <load_address>0x14384</load_address>
         <run_address>0x14384</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_info</name>
         <load_address>0x14517</load_address>
         <run_address>0x14517</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-1ed">
         <name>.debug_info</name>
         <load_address>0x146ae</load_address>
         <run_address>0x146ae</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_info</name>
         <load_address>0x148c5</load_address>
         <run_address>0x148c5</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0x14a5e</load_address>
         <run_address>0x14a5e</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_info</name>
         <load_address>0x14c13</load_address>
         <run_address>0x14c13</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-21a">
         <name>.debug_info</name>
         <load_address>0x14dcf</load_address>
         <run_address>0x14dcf</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_info</name>
         <load_address>0x150c8</load_address>
         <run_address>0x150c8</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_info</name>
         <load_address>0x1514d</load_address>
         <run_address>0x1514d</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_info</name>
         <load_address>0x15447</load_address>
         <run_address>0x15447</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-268">
         <name>.debug_info</name>
         <load_address>0x1568b</load_address>
         <run_address>0x1568b</run_address>
         <size>0x93</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_ranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x1f8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x2a8</load_address>
         <run_address>0x2a8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_ranges</name>
         <load_address>0x2c0</load_address>
         <run_address>0x2c0</run_address>
         <size>0xb0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_ranges</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_ranges</name>
         <load_address>0x418</load_address>
         <run_address>0x418</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_ranges</name>
         <load_address>0x468</load_address>
         <run_address>0x468</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0x490</load_address>
         <run_address>0x490</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_ranges</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_ranges</name>
         <load_address>0x550</load_address>
         <run_address>0x550</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_ranges</name>
         <load_address>0x610</load_address>
         <run_address>0x610</run_address>
         <size>0x78</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.debug_ranges</name>
         <load_address>0x688</load_address>
         <run_address>0x688</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_ranges</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_ranges</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.debug_ranges</name>
         <load_address>0xa50</load_address>
         <run_address>0xa50</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.debug_ranges</name>
         <load_address>0xbf8</load_address>
         <run_address>0xbf8</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_ranges</name>
         <load_address>0xda0</load_address>
         <run_address>0xda0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_ranges</name>
         <load_address>0xde8</load_address>
         <run_address>0xde8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_ranges</name>
         <load_address>0xe30</load_address>
         <run_address>0xe30</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_ranges</name>
         <load_address>0xe48</load_address>
         <run_address>0xe48</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_ranges</name>
         <load_address>0xe98</load_address>
         <run_address>0xe98</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_ranges</name>
         <load_address>0xeb0</load_address>
         <run_address>0xeb0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-21b">
         <name>.debug_ranges</name>
         <load_address>0xed8</load_address>
         <run_address>0xed8</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_ranges</name>
         <load_address>0xf10</load_address>
         <run_address>0xf10</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0xf28</load_address>
         <run_address>0xf28</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_ranges</name>
         <load_address>0xf50</load_address>
         <run_address>0xf50</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x16f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_str</name>
         <load_address>0x16f</load_address>
         <run_address>0x16f</run_address>
         <size>0x4b6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_str</name>
         <load_address>0x625</load_address>
         <run_address>0x625</run_address>
         <size>0xfd9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_str</name>
         <load_address>0x15fe</load_address>
         <run_address>0x15fe</run_address>
         <size>0x3609</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x4c07</load_address>
         <run_address>0x4c07</run_address>
         <size>0x14a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_str</name>
         <load_address>0x4d51</load_address>
         <run_address>0x4d51</run_address>
         <size>0x635</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_str</name>
         <load_address>0x5386</load_address>
         <run_address>0x5386</run_address>
         <size>0x8ec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.debug_str</name>
         <load_address>0x5c72</load_address>
         <run_address>0x5c72</run_address>
         <size>0x23b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_str</name>
         <load_address>0x5ead</load_address>
         <run_address>0x5ead</run_address>
         <size>0x4d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_str</name>
         <load_address>0x637d</load_address>
         <run_address>0x637d</run_address>
         <size>0x5e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x6961</load_address>
         <run_address>0x6961</run_address>
         <size>0x8ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_str</name>
         <load_address>0x7250</load_address>
         <run_address>0x7250</run_address>
         <size>0x6bb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_str</name>
         <load_address>0x790b</load_address>
         <run_address>0x790b</run_address>
         <size>0x8d2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-20a">
         <name>.debug_str</name>
         <load_address>0x81dd</load_address>
         <run_address>0x81dd</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-202">
         <name>.debug_str</name>
         <load_address>0x880e</load_address>
         <run_address>0x880e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_str</name>
         <load_address>0x897b</load_address>
         <run_address>0x897b</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_str</name>
         <load_address>0x922a</load_address>
         <run_address>0x922a</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-208">
         <name>.debug_str</name>
         <load_address>0xaff6</load_address>
         <run_address>0xaff6</run_address>
         <size>0xce3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-206">
         <name>.debug_str</name>
         <load_address>0xbcd9</load_address>
         <run_address>0xbcd9</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_str</name>
         <load_address>0xcd4e</load_address>
         <run_address>0xcd4e</run_address>
         <size>0x10f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_str</name>
         <load_address>0xce5d</load_address>
         <run_address>0xce5d</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_str</name>
         <load_address>0xd082</load_address>
         <run_address>0xd082</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_str</name>
         <load_address>0xd3b1</load_address>
         <run_address>0xd3b1</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_str</name>
         <load_address>0xd4a6</load_address>
         <run_address>0xd4a6</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_str</name>
         <load_address>0xd641</load_address>
         <run_address>0xd641</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_str</name>
         <load_address>0xd7a9</load_address>
         <run_address>0xd7a9</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_str</name>
         <load_address>0xd97e</load_address>
         <run_address>0xd97e</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-1f4">
         <name>.debug_str</name>
         <load_address>0xdac6</load_address>
         <run_address>0xdac6</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-222">
         <name>.debug_str</name>
         <load_address>0xdbaf</load_address>
         <run_address>0xdbaf</run_address>
         <size>0x276</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1ff">
         <name>.debug_str</name>
         <load_address>0xde25</load_address>
         <run_address>0xde25</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x3c</load_address>
         <run_address>0x3c</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_frame</name>
         <load_address>0xc4</load_address>
         <run_address>0xc4</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_frame</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x5b4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x77c</load_address>
         <run_address>0x77c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_frame</name>
         <load_address>0x7ac</load_address>
         <run_address>0x7ac</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_frame</name>
         <load_address>0x998</load_address>
         <run_address>0x998</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0xa70</load_address>
         <run_address>0xa70</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0xad8</load_address>
         <run_address>0xad8</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0xb64</load_address>
         <run_address>0xb64</run_address>
         <size>0x1b0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_frame</name>
         <load_address>0xd14</load_address>
         <run_address>0xd14</run_address>
         <size>0x298</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_frame</name>
         <load_address>0xfac</load_address>
         <run_address>0xfac</run_address>
         <size>0x15c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_frame</name>
         <load_address>0x1108</load_address>
         <run_address>0x1108</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-191">
         <name>.debug_frame</name>
         <load_address>0x1154</load_address>
         <run_address>0x1154</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_frame</name>
         <load_address>0x1174</load_address>
         <run_address>0x1174</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_frame</name>
         <load_address>0x12a0</load_address>
         <run_address>0x12a0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.debug_frame</name>
         <load_address>0x16a8</load_address>
         <run_address>0x16a8</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.debug_frame</name>
         <load_address>0x1860</load_address>
         <run_address>0x1860</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_frame</name>
         <load_address>0x198c</load_address>
         <run_address>0x198c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x19b4</load_address>
         <run_address>0x19b4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_frame</name>
         <load_address>0x1a44</load_address>
         <run_address>0x1a44</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x1b44</load_address>
         <run_address>0x1b44</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x1b64</load_address>
         <run_address>0x1b64</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x1b9c</load_address>
         <run_address>0x1b9c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x1bc4</load_address>
         <run_address>0x1bc4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_frame</name>
         <load_address>0x1bf4</load_address>
         <run_address>0x1bf4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-167">
         <name>.debug_frame</name>
         <load_address>0x1c24</load_address>
         <run_address>0x1c24</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-219">
         <name>.debug_frame</name>
         <load_address>0x1c44</load_address>
         <run_address>0x1c44</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_frame</name>
         <load_address>0x1cb0</load_address>
         <run_address>0x1cb0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x178</load_address>
         <run_address>0x178</run_address>
         <size>0x292</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x40a</load_address>
         <run_address>0x40a</run_address>
         <size>0x5a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_line</name>
         <load_address>0x9b2</load_address>
         <run_address>0x9b2</run_address>
         <size>0xea2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x1854</load_address>
         <run_address>0x1854</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_line</name>
         <load_address>0x190c</load_address>
         <run_address>0x190c</run_address>
         <size>0x82a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_line</name>
         <load_address>0x2136</load_address>
         <run_address>0x2136</run_address>
         <size>0x8c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_line</name>
         <load_address>0x29fa</load_address>
         <run_address>0x29fa</run_address>
         <size>0x264</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x2c5e</load_address>
         <run_address>0x2c5e</run_address>
         <size>0x2e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_line</name>
         <load_address>0x2f3f</load_address>
         <run_address>0x2f3f</run_address>
         <size>0x42e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0x336d</load_address>
         <run_address>0x336d</run_address>
         <size>0x863</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x3bd0</load_address>
         <run_address>0x3bd0</run_address>
         <size>0x10ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x4c7c</load_address>
         <run_address>0x4c7c</run_address>
         <size>0x509</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.debug_line</name>
         <load_address>0x5185</load_address>
         <run_address>0x5185</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_line</name>
         <load_address>0x5404</load_address>
         <run_address>0x5404</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_line</name>
         <load_address>0x557c</load_address>
         <run_address>0x557c</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_line</name>
         <load_address>0x5bfe</load_address>
         <run_address>0x5bfe</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.debug_line</name>
         <load_address>0x736c</load_address>
         <run_address>0x736c</run_address>
         <size>0xa17</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.debug_line</name>
         <load_address>0x7d83</load_address>
         <run_address>0x7d83</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_line</name>
         <load_address>0x8705</load_address>
         <run_address>0x8705</run_address>
         <size>0xce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x87d3</load_address>
         <run_address>0x87d3</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_line</name>
         <load_address>0x89af</load_address>
         <run_address>0x89af</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_line</name>
         <load_address>0x8ec9</load_address>
         <run_address>0x8ec9</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_line</name>
         <load_address>0x8f07</load_address>
         <run_address>0x8f07</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x9005</load_address>
         <run_address>0x9005</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x90c5</load_address>
         <run_address>0x90c5</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_line</name>
         <load_address>0x928d</load_address>
         <run_address>0x928d</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_line</name>
         <load_address>0x92f4</load_address>
         <run_address>0x92f4</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-154">
         <name>.debug_line</name>
         <load_address>0x9335</load_address>
         <run_address>0x9335</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_line</name>
         <load_address>0x949a</load_address>
         <run_address>0x949a</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-215">
         <name>.debug_line</name>
         <load_address>0x95a6</load_address>
         <run_address>0x95a6</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_line</name>
         <load_address>0x965f</load_address>
         <run_address>0x965f</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1e9">
         <name>.debug_line</name>
         <load_address>0x9781</load_address>
         <run_address>0x9781</run_address>
         <size>0xc1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_line</name>
         <load_address>0x9842</load_address>
         <run_address>0x9842</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_line</name>
         <load_address>0x98f6</load_address>
         <run_address>0x98f6</run_address>
         <size>0xb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-1ef">
         <name>.debug_line</name>
         <load_address>0x99a8</load_address>
         <run_address>0x99a8</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_line</name>
         <load_address>0x9a6f</load_address>
         <run_address>0x9a6f</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_line</name>
         <load_address>0x9b13</load_address>
         <run_address>0x9b13</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_line</name>
         <load_address>0x9bcd</load_address>
         <run_address>0x9bcd</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-21d">
         <name>.debug_line</name>
         <load_address>0x9c8f</load_address>
         <run_address>0x9c8f</run_address>
         <size>0x2ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_line</name>
         <load_address>0x9f7e</load_address>
         <run_address>0x9f7e</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_line</name>
         <load_address>0xa033</load_address>
         <run_address>0xa033</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0xa0d3</load_address>
         <run_address>0xa0d3</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-203">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_loc</name>
         <load_address>0x42c</load_address>
         <run_address>0x42c</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_loc</name>
         <load_address>0x1e53</load_address>
         <run_address>0x1e53</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.debug_loc</name>
         <load_address>0x260f</load_address>
         <run_address>0x260f</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_loc</name>
         <load_address>0x2a23</load_address>
         <run_address>0x2a23</run_address>
         <size>0xa5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x2ac8</load_address>
         <run_address>0x2ac8</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_loc</name>
         <load_address>0x2ba0</load_address>
         <run_address>0x2ba0</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_loc</name>
         <load_address>0x2fc4</load_address>
         <run_address>0x2fc4</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_loc</name>
         <load_address>0x3130</load_address>
         <run_address>0x3130</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_loc</name>
         <load_address>0x319f</load_address>
         <run_address>0x319f</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_loc</name>
         <load_address>0x3306</load_address>
         <run_address>0x3306</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-21c">
         <name>.debug_loc</name>
         <load_address>0x332c</load_address>
         <run_address>0x332c</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-200">
         <name>.debug_loc</name>
         <load_address>0x368f</load_address>
         <run_address>0x368f</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-214">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1eb">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-1ee">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_aranges</name>
         <load_address>0x148</load_address>
         <run_address>0x148</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_aranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_aranges</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x3010</size>
         <contents>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-21e"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-218"/>
            <object_component_ref idref="oc-1ec"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-1e8"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-212"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-210"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-204"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-20c"/>
            <object_component_ref idref="oc-20e"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-20f"/>
            <object_component_ref idref="oc-20b"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-20d"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-a5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4b28</load_address>
         <run_address>0x4b28</run_address>
         <size>0x60</size>
         <contents>
            <object_component_ref idref="oc-264"/>
            <object_component_ref idref="oc-262"/>
            <object_component_ref idref="oc-265"/>
            <object_component_ref idref="oc-263"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x30d0</load_address>
         <run_address>0x30d0</run_address>
         <size>0x1a58</size>
         <contents>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-1cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-22c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200560</run_address>
         <size>0x23e</size>
         <contents>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-7a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x55d</size>
         <contents>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-bf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-267"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-223" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-224" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-225" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-226" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-227" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-228" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-22a" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-246" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2528</size>
         <contents>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-209"/>
            <object_component_ref idref="oc-201"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-207"/>
            <object_component_ref idref="oc-205"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-1f3"/>
            <object_component_ref idref="oc-1f1"/>
            <object_component_ref idref="oc-216"/>
            <object_component_ref idref="oc-220"/>
            <object_component_ref idref="oc-1f2"/>
            <object_component_ref idref="oc-217"/>
            <object_component_ref idref="oc-211"/>
            <object_component_ref idref="oc-1f0"/>
            <object_component_ref idref="oc-21f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-221"/>
            <object_component_ref idref="oc-1fe"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-269"/>
         </contents>
      </logical_group>
      <logical_group id="lg-248" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1571e</size>
         <contents>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-213"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-1ea"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-1ed"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-21a"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-268"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24a" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf78</size>
         <contents>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-21b"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-135"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24c" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdfb8</size>
         <contents>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-20a"/>
            <object_component_ref idref="oc-202"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-208"/>
            <object_component_ref idref="oc-206"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-1f4"/>
            <object_component_ref idref="oc-222"/>
            <object_component_ref idref="oc-1ff"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24e" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1ce0</size>
         <contents>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-219"/>
            <object_component_ref idref="oc-178"/>
         </contents>
      </logical_group>
      <logical_group id="lg-250" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa153</size>
         <contents>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-215"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-1e9"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-1ef"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-21d"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-137"/>
         </contents>
      </logical_group>
      <logical_group id="lg-252" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x36af</size>
         <contents>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-203"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-21c"/>
            <object_component_ref idref="oc-200"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25c" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b8</size>
         <contents>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-214"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-1eb"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-1ee"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-136"/>
         </contents>
      </logical_group>
      <logical_group id="lg-266" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-277" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4b88</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-278" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x79e</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-279" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x4b88</used_space>
         <unused_space>0x1b478</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x3010</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x30d0</start_address>
               <size>0x1a58</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4b28</start_address>
               <size>0x60</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x4b88</start_address>
               <size>0x1b478</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x99b</used_space>
         <unused_space>0x7665</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-228"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-22a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x55d</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020055d</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200560</start_address>
               <size>0x23e</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020079e</start_address>
               <size>0x7662</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x4b28</load_address>
            <load_size>0x3a</load_size>
            <run_address>0x20200560</run_address>
            <run_size>0x23e</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x4b70</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x55d</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x4b78</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x4b88</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x4b88</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x4b64</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x4b70</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3c">
         <name>scheduler_init</name>
         <value>0x3099</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-3d">
         <name>task_num</name>
         <value>0x2020055c</value>
      </symbol>
      <symbol id="sm-4f">
         <name>delay_ms</name>
         <value>0x29ad</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-50">
         <name>delay_times</name>
         <value>0x20200794</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-51">
         <name>SysTick_Handler</name>
         <value>0x272d</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-52">
         <name>get_systicks</name>
         <value>0x308d</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-81">
         <name>main</name>
         <value>0xa09</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-82">
         <name>Anolog</name>
         <value>0x20200738</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-83">
         <name>rx_buff</name>
         <value>0x20200560</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-84">
         <name>white</name>
         <value>0x20200768</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-85">
         <name>black</name>
         <value>0x20200748</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-86">
         <name>Run</name>
         <value>0x20200790</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-87">
         <name>D_Num</name>
         <value>0x20200784</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-88">
         <name>encoderB_cnt</name>
         <value>0x20200550</value>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG0_IRQHandler</name>
         <value>0xe6d</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-8a">
         <name>encoderA_cnt</name>
         <value>0x2020054c</value>
      </symbol>
      <symbol id="sm-8b">
         <name>Flag_stop</name>
         <value>0x2020053c</value>
      </symbol>
      <symbol id="sm-8c">
         <name>Flag_stop1</name>
         <value>0x20200540</value>
      </symbol>
      <symbol id="sm-17f">
         <name>SYSCFG_DL_init</name>
         <value>0x2699</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-180">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1941</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-181">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x291</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-182">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x2301</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-183">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0x18b5</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-184">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x2665</value>
         <object_component_ref idref="oc-13e"/>
      </symbol>
      <symbol id="sm-185">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x1f65</value>
         <object_component_ref idref="oc-13f"/>
      </symbol>
      <symbol id="sm-186">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1fbd</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-187">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x22b9</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-188">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x3073</value>
         <object_component_ref idref="oc-142"/>
      </symbol>
      <symbol id="sm-189">
         <name>gPWM_0Backup</name>
         <value>0x20200480</value>
      </symbol>
      <symbol id="sm-194">
         <name>Default_Handler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-195">
         <name>Reset_Handler</name>
         <value>0x30c7</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-196">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-197">
         <name>NMI_Handler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-198">
         <name>HardFault_Handler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-199">
         <name>SVC_Handler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19a">
         <name>PendSV_Handler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19b">
         <name>GROUP0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19c">
         <name>TIMG8_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19d">
         <name>UART3_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19e">
         <name>ADC0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19f">
         <name>ADC1_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a0">
         <name>CANFD0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a1">
         <name>DAC0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a2">
         <name>SPI0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>SPI1_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>UART1_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a5">
         <name>UART2_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a6">
         <name>TIMG6_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a7">
         <name>TIMA0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a8">
         <name>TIMA1_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1a9">
         <name>TIMG7_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>TIMG12_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1ab">
         <name>I2C0_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1ac">
         <name>I2C1_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1ad">
         <name>AES_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>RTC_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1af">
         <name>DMA_IRQHandler</name>
         <value>0x30bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1b6">
         <name>track_ctrl</name>
         <value>0x20200718</value>
         <object_component_ref idref="oc-12f"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>Get_Analog_value</name>
         <value>0x14cd</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-1ee">
         <name>adc_getValue</name>
         <value>0x21df</value>
         <object_component_ref idref="oc-1df"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>convertAnalogToDigital</name>
         <value>0x1ca5</value>
         <object_component_ref idref="oc-14b"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>normalizeAnalogValues</name>
         <value>0x159d</value>
         <object_component_ref idref="oc-14c"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>No_MCU_Ganv_Sensor_Init_Frist</name>
         <value>0x1bc5</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-1f2">
         <name>No_MCU_Ganv_Sensor_Init</name>
         <value>0x5b9</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-1f3">
         <name>No_Mcu_Ganv_Sensor_Task_Without_tick</name>
         <value>0x238d</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>Get_Digtal_For_User</name>
         <value>0x3049</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>Get_Anolog_Value</name>
         <value>0x2549</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-214">
         <name>Square_Track_Init</name>
         <value>0x2b75</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-215">
         <name>square_ctrl</name>
         <value>0x20200758</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-216">
         <name>Square_Analyze_State</name>
         <value>0xf6d</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-217">
         <name>Square_Track_Control</name>
         <value>0x1ea5</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-218">
         <name>Square_Go_Straight</name>
         <value>0x2f61</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-219">
         <name>Square_Turn_Left</name>
         <value>0x2f75</value>
         <object_component_ref idref="oc-161"/>
      </symbol>
      <symbol id="sm-21a">
         <name>Square_Turn_Right</name>
         <value>0x2f89</value>
         <object_component_ref idref="oc-162"/>
      </symbol>
      <symbol id="sm-21b">
         <name>Square_Handle_Lost</name>
         <value>0x3057</value>
         <object_component_ref idref="oc-163"/>
      </symbol>
      <symbol id="sm-231">
         <name>GROUP1_IRQHandler</name>
         <value>0xb3d</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-232">
         <name>gpio_interrup1</name>
         <value>0x20200554</value>
      </symbol>
      <symbol id="sm-233">
         <name>gpio_interrup2</name>
         <value>0x20200558</value>
      </symbol>
      <symbol id="sm-234">
         <name>Get_Encoder_countA</name>
         <value>0x20200544</value>
      </symbol>
      <symbol id="sm-235">
         <name>Get_Encoder_countB</name>
         <value>0x20200548</value>
      </symbol>
      <symbol id="sm-254">
         <name>Key</name>
         <value>0x2411</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-255">
         <name>Key_System_Tick_Inc</name>
         <value>0x3029</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-256">
         <name>Key_Init_Debounce</name>
         <value>0x2f4d</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-257">
         <name>key1_ctrl</name>
         <value>0x20200778</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-258">
         <name>Key_Scan_Debounce</name>
         <value>0x8ad</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-259">
         <name>Key_1</name>
         <value>0x1d11</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-270">
         <name>Set_PWM</name>
         <value>0x741</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-271">
         <name>Motor_Speed_Monitor</name>
         <value>0x1649</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-2a1">
         <name>OLED_ColorTurn</name>
         <value>0x2631</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-2a2">
         <name>OLED_WR_Byte</name>
         <value>0x1c39</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-2a3">
         <name>OLED_DisplayTurn</name>
         <value>0x2271</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-2a4">
         <name>OLED_Refresh</name>
         <value>0x19cd</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-2a5">
         <name>OLED_GRAM</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2a6">
         <name>OLED_Clear</name>
         <value>0x1e45</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-2a7">
         <name>OLED_DrawPoint</name>
         <value>0x1825</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-2a8">
         <name>OLED_ShowChar</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-2a9">
         <name>asc2_2412</name>
         <value>0x30d0</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>asc2_1608</name>
         <value>0x3e2c</value>
         <object_component_ref idref="oc-11a"/>
      </symbol>
      <symbol id="sm-2ab">
         <name>asc2_1206</name>
         <value>0x441c</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-2ac">
         <name>asc2_0806</name>
         <value>0x4890</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-2ad">
         <name>OLED_ShowString</name>
         <value>0x178b</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-2ae">
         <name>OLED_Pow</name>
         <value>0x26fd</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-2af">
         <name>OLED_ShowNum</name>
         <value>0x1231</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-2b0">
         <name>OLED_ShowSignedNum</name>
         <value>0x16f1</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-2b1">
         <name>OLED_Init</name>
         <value>0x1313</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-2c2">
         <name>UART0_IRQHandler</name>
         <value>0x2451</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-2c3">
         <name>uart_rx_ticks</name>
         <value>0x2020079d</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>uart_rx_index</name>
         <value>0x2020079c</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-2c5">
         <name>uart_rx_buffer</name>
         <value>0x20200660</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-2c6">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2c7">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2c8">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2c9">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2ca">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2cb">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2cc">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2cd">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2ce">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2d9">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x23d1</value>
         <object_component_ref idref="oc-1d1"/>
      </symbol>
      <symbol id="sm-2e2">
         <name>DL_Common_delayCycles</name>
         <value>0x30a5</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-2ee">
         <name>DL_I2C_setClockConfig</name>
         <value>0x2923</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-2ef">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x1f05</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-30b">
         <name>DL_Timer_setClockConfig</name>
         <value>0x2b3d</value>
         <object_component_ref idref="oc-1ad"/>
      </symbol>
      <symbol id="sm-30c">
         <name>DL_Timer_initTimerMode</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-1b6"/>
      </symbol>
      <symbol id="sm-30d">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x3019</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-30e">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x2b21</value>
         <object_component_ref idref="oc-1b1"/>
      </symbol>
      <symbol id="sm-30f">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x2da1</value>
         <object_component_ref idref="oc-1b0"/>
      </symbol>
      <symbol id="sm-310">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xd69</value>
         <object_component_ref idref="oc-1ae"/>
      </symbol>
      <symbol id="sm-31d">
         <name>DL_UART_init</name>
         <value>0x2229</value>
         <object_component_ref idref="oc-1ca"/>
      </symbol>
      <symbol id="sm-31e">
         <name>DL_UART_setClockConfig</name>
         <value>0x2fc1</value>
         <object_component_ref idref="oc-1c4"/>
      </symbol>
      <symbol id="sm-32c">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x13f1</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-32d">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x2349</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-344">
         <name>strncpy</name>
         <value>0x27e1</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-34f">
         <name>_c_int00_noargs</name>
         <value>0x28d5</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-350">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-35c">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x2585</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-364">
         <name>_system_pre_init</name>
         <value>0x30cb</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-36f">
         <name>__TI_zero_init</name>
         <value>0x3039</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-378">
         <name>__TI_decompress_none</name>
         <value>0x2fe5</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-383">
         <name>__TI_decompress_lzss</name>
         <value>0x1ad5</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-38f">
         <name>abort</name>
         <value>0x30b9</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-398">
         <name>HOSTexit</name>
         <value>0x30c3</value>
         <object_component_ref idref="oc-164"/>
      </symbol>
      <symbol id="sm-399">
         <name>C$$EXIT</name>
         <value>0x30c2</value>
         <object_component_ref idref="oc-164"/>
      </symbol>
      <symbol id="sm-3a9">
         <name>__aeabi_dadd</name>
         <value>0x42f</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-3aa">
         <name>__adddf3</name>
         <value>0x42f</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-3ab">
         <name>__aeabi_dsub</name>
         <value>0x425</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-3ac">
         <name>__subdf3</name>
         <value>0x425</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-3b2">
         <name>__aeabi_dmul</name>
         <value>0x114d</value>
         <object_component_ref idref="oc-1e4"/>
      </symbol>
      <symbol id="sm-3b3">
         <name>__muldf3</name>
         <value>0x114d</value>
         <object_component_ref idref="oc-1e4"/>
      </symbol>
      <symbol id="sm-3b9">
         <name>__muldsi3</name>
         <value>0x25c1</value>
         <object_component_ref idref="oc-212"/>
      </symbol>
      <symbol id="sm-3bf">
         <name>__aeabi_ddiv</name>
         <value>0xc5d</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-3c0">
         <name>__divdf3</name>
         <value>0xc5d</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-3c6">
         <name>__aeabi_d2iz</name>
         <value>0x2195</value>
         <object_component_ref idref="oc-1e8"/>
      </symbol>
      <symbol id="sm-3c7">
         <name>__fixdfsi</name>
         <value>0x2195</value>
         <object_component_ref idref="oc-1e8"/>
      </symbol>
      <symbol id="sm-3cd">
         <name>__aeabi_i2d</name>
         <value>0x27b5</value>
         <object_component_ref idref="oc-1e0"/>
      </symbol>
      <symbol id="sm-3ce">
         <name>__floatsidf</name>
         <value>0x27b5</value>
         <object_component_ref idref="oc-1e0"/>
      </symbol>
      <symbol id="sm-3d4">
         <name>__aeabi_ui2d</name>
         <value>0x2949</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-3d5">
         <name>__floatunsidf</name>
         <value>0x2949</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-3db">
         <name>__aeabi_dcmpeq</name>
         <value>0x1de1</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3dc">
         <name>__aeabi_dcmplt</name>
         <value>0x1df5</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3dd">
         <name>__aeabi_dcmple</name>
         <value>0x1e09</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3de">
         <name>__aeabi_dcmpge</name>
         <value>0x1e1d</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3df">
         <name>__aeabi_dcmpgt</name>
         <value>0x1e31</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3e5">
         <name>__aeabi_memcpy</name>
         <value>0x30b1</value>
         <object_component_ref idref="oc-53"/>
      </symbol>
      <symbol id="sm-3e6">
         <name>__aeabi_memcpy4</name>
         <value>0x30b1</value>
         <object_component_ref idref="oc-53"/>
      </symbol>
      <symbol id="sm-3e7">
         <name>__aeabi_memcpy8</name>
         <value>0x30b1</value>
         <object_component_ref idref="oc-53"/>
      </symbol>
      <symbol id="sm-3ee">
         <name>__aeabi_memclr</name>
         <value>0x3081</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-3ef">
         <name>__aeabi_memclr4</name>
         <value>0x3081</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-3f0">
         <name>__aeabi_memclr8</name>
         <value>0x3081</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-3f6">
         <name>__aeabi_uidiv</name>
         <value>0x2491</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-3f7">
         <name>__aeabi_uidivmod</name>
         <value>0x2491</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-405">
         <name>__ledf2</name>
         <value>0x1d79</value>
         <object_component_ref idref="oc-218"/>
      </symbol>
      <symbol id="sm-406">
         <name>__gedf2</name>
         <value>0x1b51</value>
         <object_component_ref idref="oc-21e"/>
      </symbol>
      <symbol id="sm-407">
         <name>__cmpdf2</name>
         <value>0x1d79</value>
         <object_component_ref idref="oc-218"/>
      </symbol>
      <symbol id="sm-408">
         <name>__eqdf2</name>
         <value>0x1d79</value>
         <object_component_ref idref="oc-218"/>
      </symbol>
      <symbol id="sm-409">
         <name>__ltdf2</name>
         <value>0x1d79</value>
         <object_component_ref idref="oc-218"/>
      </symbol>
      <symbol id="sm-40a">
         <name>__nedf2</name>
         <value>0x1d79</value>
         <object_component_ref idref="oc-218"/>
      </symbol>
      <symbol id="sm-40b">
         <name>__gtdf2</name>
         <value>0x1b51</value>
         <object_component_ref idref="oc-21e"/>
      </symbol>
      <symbol id="sm-415">
         <name>__aeabi_idiv0</name>
         <value>0x5b7</value>
         <object_component_ref idref="oc-174"/>
      </symbol>
      <symbol id="sm-41e">
         <name>TI_memcpy_small</name>
         <value>0x2fd3</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-427">
         <name>TI_memset_small</name>
         <value>0x3065</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-428">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-42b">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-42c">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>

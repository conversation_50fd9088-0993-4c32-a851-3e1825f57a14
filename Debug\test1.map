******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:32:17 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000028d5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004b88  0001b478  R  X
  SRAM                  20200000   00008000  0000099b  00007665  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004b88   00004b88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003010   00003010    r-x .text
  000030d0    000030d0    00001a58   00001a58    r-- .rodata
  00004b28    00004b28    00000060   00000060    r-- .cinit
20200000    20200000    0000079e   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000023e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003010     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000134     empty.o (.text.main)
                  00000b3c    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000c5c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000d68    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000e6c    00000100     empty.o (.text.TIMG0_IRQHandler)
                  00000f6c    000000f8     Square_Track_Simple.o (.text.Square_Analyze_State)
                  00001064    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000114c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001230    000000e2     oled.o (.text.OLED_ShowNum)
                  00001312    000000de     oled.o (.text.OLED_Init)
                  000013f0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000014cc    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000159c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001646    00000002     --HOLE-- [fill = 0]
                  00001648    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  000016f0    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  0000178a    0000009a     oled.o (.text.OLED_ShowString)
                  00001824    00000090     oled.o (.text.OLED_DrawPoint)
                  000018b4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001940    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000019cc    00000084     oled.o (.text.OLED_Refresh)
                  00001a50    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001ad4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001b50    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001bc4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001c36    00000002     --HOLE-- [fill = 0]
                  00001c38    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001ca4    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001d10    00000068     key.o (.text.Key_1)
                  00001d78    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001de0    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001e42    00000002     --HOLE-- [fill = 0]
                  00001e44    00000060     oled.o (.text.OLED_Clear)
                  00001ea4    00000060     Square_Track_Simple.o (.text.Square_Track_Control)
                  00001f04    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00001f62    00000002     --HOLE-- [fill = 0]
                  00001f64    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00001fbc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002010    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002060    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000020b0    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  000020fc    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002148    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002192    00000002     --HOLE-- [fill = 0]
                  00002194    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000021de    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002228    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002270    00000048     oled.o (.text.OLED_DisplayTurn)
                  000022b8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002300    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002348    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000238c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000023ce    00000002     --HOLE-- [fill = 0]
                  000023d0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002410    00000040     key.o (.text.Key)
                  00002450    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002490    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000024d0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000250c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002548    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002584    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000025c0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000025fa    00000002     --HOLE-- [fill = 0]
                  000025fc    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002630    00000034     oled.o (.text.OLED_ColorTurn)
                  00002664    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002698    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000026cc    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  000026fc    00000030     oled.o (.text.OLED_Pow)
                  0000272c    00000030     systick.o (.text.SysTick_Handler)
                  0000275c    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002788    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000027b4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000027e0    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  0000280c    00000028     empty.o (.text.DL_Common_updateReg)
                  00002834    00000028     oled.o (.text.DL_Common_updateReg)
                  0000285c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002884    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000028ac    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000028d4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000028fc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002922    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002948    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000296c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000298c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000029ac    00000020     systick.o (.text.delay_ms)
                  000029cc    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000029ea    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002a08    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002a24    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002a40    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002a5c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002a78    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002a94    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002ab0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002acc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002ae8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002b04    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002b20    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002b3c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002b58    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002b74    0000001c     Square_Track_Simple.o (.text.Square_Track_Init)
                  00002b90    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002ba8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002bc0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002bd8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002bf0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002c08    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002c20    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002c38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002c50    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002c68    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002c80    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002c98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002cb0    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00002cc8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002ce0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002cf8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002d10    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002d28    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002d40    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002d58    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002d70    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002d88    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002da0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002db8    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002dd0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002de8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002e00    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002e16    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002e2c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002e42    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002e58    00000016     key.o (.text.DL_GPIO_readPins)
                  00002e6e    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002e84    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002e98    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002eac    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00002ec0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002ed4    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00002ee8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00002efc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002f10    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002f24    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002f38    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00002f4c    00000014     key.o (.text.Key_Init_Debounce)
                  00002f60    00000014     Square_Track_Simple.o (.text.Square_Go_Straight)
                  00002f74    00000014     Square_Track_Simple.o (.text.Square_Turn_Left)
                  00002f88    00000014     Square_Track_Simple.o (.text.Square_Turn_Right)
                  00002f9c    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00002fae    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00002fc0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002fd2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002fe4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002ff6    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003006    00000002     --HOLE-- [fill = 0]
                  00003008    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003018    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003028    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003038    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003048    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003056    0000000e     Square_Track_Simple.o (.text.Square_Handle_Lost)
                  00003064    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003072    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000307e    00000002     --HOLE-- [fill = 0]
                  00003080    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000308c    0000000c     systick.o (.text.get_systicks)
                  00003098    0000000c     Scheduler.o (.text.scheduler_init)
                  000030a4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000030ae    00000002     --HOLE-- [fill = 0]
                  000030b0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000030b8    00000006     libc.a : exit.c.obj (.text:abort)
                  000030be    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000030c2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000030c6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000030ca    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000030ce    00000002     --HOLE-- [fill = 0]

.cinit     0    00004b28    00000060     
                  00004b28    0000003a     (.cinit..data.load) [load image, compression = lzss]
                  00004b62    00000002     --HOLE-- [fill = 0]
                  00004b64    0000000c     (__TI_handler_table)
                  00004b70    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004b78    00000010     (__TI_cinit_table)

.rodata    0    000030d0    00001a58     
                  000030d0    00000d5c     oled.o (.rodata.asc2_2412)
                  00003e2c    000005f0     oled.o (.rodata.asc2_1608)
                  0000441c    00000474     oled.o (.rodata.asc2_1206)
                  00004890    00000228     oled.o (.rodata.asc2_0806)
                  00004ab8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004ae0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004af4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004afe    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004b00    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004b08    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004b10    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00004b18    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004b1b    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004b1e    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004b21    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004b23    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000023e     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  20200738    00000010     empty.o (.data.Anolog)
                  20200748    00000010     empty.o (.data.black)
                  20200758    00000010     Square_Track_Simple.o (.data.square_ctrl)
                  20200768    00000010     empty.o (.data.white)
                  20200778    0000000c     key.o (.data.key1_ctrl)
                  20200784    00000004     empty.o (.data.D_Num)
                  20200788    00000008     systick.o (.data.systicks)
                  20200790    00000004     empty.o (.data.Run)
                  20200794    00000004     systick.o (.data.delay_times)
                  20200798    00000004     key.o (.data.system_tick_ms)
                  2020079c    00000001     bsp_usart.o (.data.uart_rx_index)
                  2020079d    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          878     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3658    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       motor.o                          576     8         56     
       key.o                            574     0         16     
       Square_Track_Simple.o            446     0         16     
       encoder.o                        362     0         16     
       Ganway_Optimized.o               0       0         32     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3384    8         137    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       strncpy.c.obj                    44      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           344     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       94        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12282   7025      2459   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004b78 records: 2, size/record: 8, table size: 16
	.data: load addr=00004b28, load size=0000003a bytes, run addr=20200560, run size=0000023e bytes, compression=lzss
	.bss: load addr=00004b70, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004b64 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000030bf  ADC0_IRQHandler                      
000030bf  ADC1_IRQHandler                      
000030bf  AES_IRQHandler                       
20200738  Anolog                               
000030c2  C$$EXIT                              
000030bf  CANFD0_IRQHandler                    
000030bf  DAC0_IRQHandler                      
000023d1  DL_ADC12_setClockConfig              
000030a5  DL_Common_delayCycles                
00001f05  DL_I2C_fillControllerTXFIFO          
00002923  DL_I2C_setClockConfig                
000013f1  DL_SYSCTL_configSYSPLL               
00002349  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000d69  DL_Timer_initFourCCPWMMode           
00001065  DL_Timer_initTimerMode               
00002b21  DL_Timer_setCaptCompUpdateMethod     
00002da1  DL_Timer_setCaptureCompareOutCtl     
00003019  DL_Timer_setCaptureCompareValue      
00002b3d  DL_Timer_setClockConfig              
00002229  DL_UART_init                         
00002fc1  DL_UART_setClockConfig               
000030bf  DMA_IRQHandler                       
20200784  D_Num                                
000030bf  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000030bf  GROUP0_IRQHandler                    
00000b3d  GROUP1_IRQHandler                    
000014cd  Get_Analog_value                     
00002549  Get_Anolog_Value                     
00003049  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000030c3  HOSTexit                             
000030bf  HardFault_Handler                    
000030bf  I2C0_IRQHandler                      
000030bf  I2C1_IRQHandler                      
00002411  Key                                  
00001d11  Key_1                                
00002f4d  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003029  Key_System_Tick_Inc                  
00001649  Motor_Speed_Monitor                  
000030bf  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
00001bc5  No_MCU_Ganv_Sensor_Init_Frist        
0000238d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001e45  OLED_Clear                           
00002631  OLED_ColorTurn                       
00002271  OLED_DisplayTurn                     
00001825  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001313  OLED_Init                            
000026fd  OLED_Pow                             
000019cd  OLED_Refresh                         
000000c1  OLED_ShowChar                        
00001231  OLED_ShowNum                         
000016f1  OLED_ShowSignedNum                   
0000178b  OLED_ShowString                      
00001c39  OLED_WR_Byte                         
000030bf  PendSV_Handler                       
000030bf  RTC_IRQHandler                       
000030c7  Reset_Handler                        
20200790  Run                                  
000030bf  SPI0_IRQHandler                      
000030bf  SPI1_IRQHandler                      
000030bf  SVC_Handler                          
000022b9  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
00001f65  SYSCFG_DL_I2C_OLED_init              
000018b5  SYSCFG_DL_PWM_0_init                 
00002301  SYSCFG_DL_SYSCTL_init                
00003073  SYSCFG_DL_SYSTICK_init               
00002665  SYSCFG_DL_TIMER_0_init               
00001fbd  SYSCFG_DL_UART_0_init                
00002699  SYSCFG_DL_init                       
00001941  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
00000f6d  Square_Analyze_State                 
00002f61  Square_Go_Straight                   
00003057  Square_Handle_Lost                   
00001ea5  Square_Track_Control                 
00002b75  Square_Track_Init                    
00002f75  Square_Turn_Left                     
00002f89  Square_Turn_Right                    
0000272d  SysTick_Handler                      
000030bf  TIMA0_IRQHandler                     
000030bf  TIMA1_IRQHandler                     
00000e6d  TIMG0_IRQHandler                     
000030bf  TIMG12_IRQHandler                    
000030bf  TIMG6_IRQHandler                     
000030bf  TIMG7_IRQHandler                     
000030bf  TIMG8_IRQHandler                     
00002fd3  TI_memcpy_small                      
00003065  TI_memset_small                      
00002451  UART0_IRQHandler                     
000030bf  UART1_IRQHandler                     
000030bf  UART2_IRQHandler                     
000030bf  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004b78  __TI_CINIT_Base                      
00004b88  __TI_CINIT_Limit                     
00004b88  __TI_CINIT_Warm                      
00004b64  __TI_Handler_Table_Base              
00004b70  __TI_Handler_Table_Limit             
00002585  __TI_auto_init_nobinit_nopinit       
00001ad5  __TI_decompress_lzss                 
00002fe5  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003039  __TI_zero_init                       
0000042f  __adddf3                             
00002195  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
00001de1  __aeabi_dcmpeq                       
00001e1d  __aeabi_dcmpge                       
00001e31  __aeabi_dcmpgt                       
00001e09  __aeabi_dcmple                       
00001df5  __aeabi_dcmplt                       
00000c5d  __aeabi_ddiv                         
0000114d  __aeabi_dmul                         
00000425  __aeabi_dsub                         
000027b5  __aeabi_i2d                          
000005b7  __aeabi_idiv0                        
00003081  __aeabi_memclr                       
00003081  __aeabi_memclr4                      
00003081  __aeabi_memclr8                      
000030b1  __aeabi_memcpy                       
000030b1  __aeabi_memcpy4                      
000030b1  __aeabi_memcpy8                      
00002949  __aeabi_ui2d                         
00002491  __aeabi_uidiv                        
00002491  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001d79  __cmpdf2                             
00000c5d  __divdf3                             
00001d79  __eqdf2                              
00002195  __fixdfsi                            
000027b5  __floatsidf                          
00002949  __floatunsidf                        
00001b51  __gedf2                              
00001b51  __gtdf2                              
00001d79  __ledf2                              
00001d79  __ltdf2                              
UNDEFED   __mpu_init                           
0000114d  __muldf3                             
000025c1  __muldsi3                            
00001d79  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
000028d5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000030cb  _system_pre_init                     
000030b9  abort                                
000021df  adc_getValue                         
00004890  asc2_0806                            
0000441c  asc2_1206                            
00003e2c  asc2_1608                            
000030d0  asc2_2412                            
ffffffff  binit                                
20200748  black                                
00001ca5  convertAnalogToDigital               
000029ad  delay_ms                             
20200794  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
0000308d  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
20200778  key1_ctrl                            
00000a09  main                                 
0000159d  normalizeAnalogValues                
20200560  rx_buff                              
00003099  scheduler_init                       
20200758  square_ctrl                          
000027e1  strncpy                              
2020055c  task_num                             
20200718  track_ctrl                           
20200660  uart_rx_buffer                       
2020079c  uart_rx_index                        
2020079d  uart_rx_ticks                        
20200768  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  main                                 
00000b3d  GROUP1_IRQHandler                    
00000c5d  __aeabi_ddiv                         
00000c5d  __divdf3                             
00000d69  DL_Timer_initFourCCPWMMode           
00000e6d  TIMG0_IRQHandler                     
00000f6d  Square_Analyze_State                 
00001065  DL_Timer_initTimerMode               
0000114d  __aeabi_dmul                         
0000114d  __muldf3                             
00001231  OLED_ShowNum                         
00001313  OLED_Init                            
000013f1  DL_SYSCTL_configSYSPLL               
000014cd  Get_Analog_value                     
0000159d  normalizeAnalogValues                
00001649  Motor_Speed_Monitor                  
000016f1  OLED_ShowSignedNum                   
0000178b  OLED_ShowString                      
00001825  OLED_DrawPoint                       
000018b5  SYSCFG_DL_PWM_0_init                 
00001941  SYSCFG_DL_initPower                  
000019cd  OLED_Refresh                         
00001ad5  __TI_decompress_lzss                 
00001b51  __gedf2                              
00001b51  __gtdf2                              
00001bc5  No_MCU_Ganv_Sensor_Init_Frist        
00001c39  OLED_WR_Byte                         
00001ca5  convertAnalogToDigital               
00001d11  Key_1                                
00001d79  __cmpdf2                             
00001d79  __eqdf2                              
00001d79  __ledf2                              
00001d79  __ltdf2                              
00001d79  __nedf2                              
00001de1  __aeabi_dcmpeq                       
00001df5  __aeabi_dcmplt                       
00001e09  __aeabi_dcmple                       
00001e1d  __aeabi_dcmpge                       
00001e31  __aeabi_dcmpgt                       
00001e45  OLED_Clear                           
00001ea5  Square_Track_Control                 
00001f05  DL_I2C_fillControllerTXFIFO          
00001f65  SYSCFG_DL_I2C_OLED_init              
00001fbd  SYSCFG_DL_UART_0_init                
00002195  __aeabi_d2iz                         
00002195  __fixdfsi                            
000021df  adc_getValue                         
00002229  DL_UART_init                         
00002271  OLED_DisplayTurn                     
000022b9  SYSCFG_DL_ADC12_0_init               
00002301  SYSCFG_DL_SYSCTL_init                
00002349  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000238d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000023d1  DL_ADC12_setClockConfig              
00002411  Key                                  
00002451  UART0_IRQHandler                     
00002491  __aeabi_uidiv                        
00002491  __aeabi_uidivmod                     
00002549  Get_Anolog_Value                     
00002585  __TI_auto_init_nobinit_nopinit       
000025c1  __muldsi3                            
00002631  OLED_ColorTurn                       
00002665  SYSCFG_DL_TIMER_0_init               
00002699  SYSCFG_DL_init                       
000026fd  OLED_Pow                             
0000272d  SysTick_Handler                      
000027b5  __aeabi_i2d                          
000027b5  __floatsidf                          
000027e1  strncpy                              
000028d5  _c_int00_noargs                      
00002923  DL_I2C_setClockConfig                
00002949  __aeabi_ui2d                         
00002949  __floatunsidf                        
000029ad  delay_ms                             
00002b21  DL_Timer_setCaptCompUpdateMethod     
00002b3d  DL_Timer_setClockConfig              
00002b75  Square_Track_Init                    
00002da1  DL_Timer_setCaptureCompareOutCtl     
00002f4d  Key_Init_Debounce                    
00002f61  Square_Go_Straight                   
00002f75  Square_Turn_Left                     
00002f89  Square_Turn_Right                    
00002fc1  DL_UART_setClockConfig               
00002fd3  TI_memcpy_small                      
00002fe5  __TI_decompress_none                 
00003019  DL_Timer_setCaptureCompareValue      
00003029  Key_System_Tick_Inc                  
00003039  __TI_zero_init                       
00003049  Get_Digtal_For_User                  
00003057  Square_Handle_Lost                   
00003065  TI_memset_small                      
00003073  SYSCFG_DL_SYSTICK_init               
00003081  __aeabi_memclr                       
00003081  __aeabi_memclr4                      
00003081  __aeabi_memclr8                      
0000308d  get_systicks                         
00003099  scheduler_init                       
000030a5  DL_Common_delayCycles                
000030b1  __aeabi_memcpy                       
000030b1  __aeabi_memcpy4                      
000030b1  __aeabi_memcpy8                      
000030b9  abort                                
000030bf  ADC0_IRQHandler                      
000030bf  ADC1_IRQHandler                      
000030bf  AES_IRQHandler                       
000030bf  CANFD0_IRQHandler                    
000030bf  DAC0_IRQHandler                      
000030bf  DMA_IRQHandler                       
000030bf  Default_Handler                      
000030bf  GROUP0_IRQHandler                    
000030bf  HardFault_Handler                    
000030bf  I2C0_IRQHandler                      
000030bf  I2C1_IRQHandler                      
000030bf  NMI_Handler                          
000030bf  PendSV_Handler                       
000030bf  RTC_IRQHandler                       
000030bf  SPI0_IRQHandler                      
000030bf  SPI1_IRQHandler                      
000030bf  SVC_Handler                          
000030bf  TIMA0_IRQHandler                     
000030bf  TIMA1_IRQHandler                     
000030bf  TIMG12_IRQHandler                    
000030bf  TIMG6_IRQHandler                     
000030bf  TIMG7_IRQHandler                     
000030bf  TIMG8_IRQHandler                     
000030bf  UART1_IRQHandler                     
000030bf  UART2_IRQHandler                     
000030bf  UART3_IRQHandler                     
000030c2  C$$EXIT                              
000030c3  HOSTexit                             
000030c7  Reset_Handler                        
000030cb  _system_pre_init                     
000030d0  asc2_2412                            
00003e2c  asc2_1608                            
0000441c  asc2_1206                            
00004890  asc2_0806                            
00004b64  __TI_Handler_Table_Base              
00004b70  __TI_Handler_Table_Limit             
00004b78  __TI_CINIT_Base                      
00004b88  __TI_CINIT_Limit                     
00004b88  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  track_ctrl                           
20200738  Anolog                               
20200748  black                                
20200758  square_ctrl                          
20200768  white                                
20200778  key1_ctrl                            
20200784  D_Num                                
20200790  Run                                  
20200794  delay_times                          
2020079c  uart_rx_index                        
2020079d  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[200 symbols]

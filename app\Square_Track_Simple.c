#include "Square_Track_Simple.h"
#include "bsp_system.h"

// 全局控制变量
Square_Control_t square_ctrl = {
    .state = SQUARE_STATE_STRAIGHT,
    .straight_speed = SQUARE_STRAIGHT_SPEED,
    .turn_speed = SQUARE_TURN_SPEED,
    .lost_count = 0
};

/**
 * @brief 正方形轨道系统初始化
 */
void Square_Track_Init(void)
{
    square_ctrl.state = SQUARE_STATE_STRAIGHT;
    square_ctrl.straight_speed = SQUARE_STRAIGHT_SPEED;
    square_ctrl.turn_speed = SQUARE_TURN_SPEED;
    square_ctrl.lost_count = 0;
}

/**
 * @brief 分析传感器状态，判断轨道情况
 */
Square_State_t Square_Analyze_State(unsigned char digital_data)
{
    // 统计检测到黑线的传感器数量
    int black_count = 0;
    for(int i = 0; i < SQUARE_SENSOR_COUNT; i++) {
        if(!((digital_data >> i) & 0x01)) {  // 0表示检测到黑线
            black_count++;
        }
    }
    
    // 状态判断逻辑（简化）
    if(black_count == 0) {
        // 没有检测到黑线
        square_ctrl.lost_count++;
        if(square_ctrl.lost_count > SQUARE_LOST_COUNT_MAX) {
            return SQUARE_STATE_LOST;
        }
        return square_ctrl.state;  // 保持当前状态
    } else {
        square_ctrl.lost_count = 0;
    }
    
    // 检测左转：左侧传感器检测到黑线
    if(!((digital_data >> 0) & 0x01) || !((digital_data >> 1) & 0x01) || !((digital_data >> 2) & 0x01)) {
        return SQUARE_STATE_TURN_LEFT;
    }
    
    // 检测右转：右侧传感器检测到黑线
    if(!((digital_data >> 5) & 0x01) || !((digital_data >> 6) & 0x01) || !((digital_data >> 7) & 0x01)) {
        return SQUARE_STATE_TURN_RIGHT;
    }
    
    // 中间传感器检测到，直线行驶
    if(!((digital_data >> 3) & 0x01) || !((digital_data >> 4) & 0x01)) {
        return SQUARE_STATE_STRAIGHT;
    }
    
    return SQUARE_STATE_STRAIGHT;  // 默认直行
}

/**
 * @brief 正方形轨道主控制函数
 */
void Square_Track_Control(unsigned char digital_data)
{
    // 分析当前状态
    square_ctrl.state = Square_Analyze_State(digital_data);
    
    // 根据状态执行相应动作
    switch(square_ctrl.state)
    {
        case SQUARE_STATE_STRAIGHT:
            Square_Go_Straight();
            break;
            
        case SQUARE_STATE_TURN_LEFT:
            Square_Turn_Left();
            break;
            
        case SQUARE_STATE_TURN_RIGHT:
            Square_Turn_Right();
            break;
            
        case SQUARE_STATE_LOST:
            Square_Handle_Lost();
            break;
            
        default:
            Square_Go_Straight();
            break;
    }
}

/**
 * @brief 直线行驶
 */
void Square_Go_Straight(void)
{
    Set_PWM(square_ctrl.straight_speed, square_ctrl.straight_speed);
}

/**
 * @brief 左转：右轮转动，左轮停止
 * 这样可以实现最小转弯半径的90度转弯
 */
void Square_Turn_Left(void)
{
    Set_PWM(0, square_ctrl.turn_speed);  // 左轮停止，右轮转动
}

/**
 * @brief 右转：左轮转动，右轮停止
 * 这样可以实现最小转弯半径的90度转弯
 */
void Square_Turn_Right(void)
{
    Set_PWM(square_ctrl.turn_speed, 0);  // 左轮转动，右轮停止
}

/**
 * @brief 处理丢线情况
 */
void Square_Handle_Lost(void)
{
    // 简单的丢线处理：缓慢前进寻找轨道
    Set_PWM(SQUARE_MIN_SPEED, SQUARE_MIN_SPEED);
}

/**
 * @brief 停止
 */
void Square_Stop(void)
{
    Set_PWM(0, 0);
}

/**
 * @brief 调试信息输出（可选）
 */
void Square_Debug_Info(void)
{
    // 可以通过OLED显示当前状态
    // OLED_ShowString(0, 0, "Square Track", 12, 1);
    // OLED_ShowNum(0, 12, square_ctrl.state, 1, 12, 1);
    // OLED_ShowNum(0, 24, square_ctrl.straight_speed, 4, 12, 1);
    // OLED_Refresh();
}

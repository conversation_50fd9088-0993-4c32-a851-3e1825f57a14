# 正方形轨道简化版循迹系统

## 🎯 设计理念

专门针对正方形轨道设计，采用**一轮停止，一轮转动**的策略实现90度直角转弯，系统极度简化，易于理解和调试。

## 🚀 核心特性

### 1. **极简设计**
- 只有3个文件：`Square_Track_Simple.h/c` + `Square_Config.h`
- 总代码量不到200行
- 无复杂的PID算法，无多余的模式切换

### 2. **专用转弯策略**
```c
// 左转：右轮转动，左轮停止
void Square_Turn_Left(void)
{
    Set_PWM(0, square_ctrl.turn_speed);  // 左轮停止，右轮转动
}

// 右转：左轮转动，右轮停止  
void Square_Turn_Right(void)
{
    Set_PWM(square_ctrl.turn_speed, 0);  // 左轮转动，右轮停止
}
```

### 3. **简单状态机**
只有4个状态：
- `SQUARE_STATE_STRAIGHT` - 直线行驶
- `SQUARE_STATE_TURN_LEFT` - 左转
- `SQUARE_STATE_TURN_RIGHT` - 右转  
- `SQUARE_STATE_LOST` - 丢线

## ⚙️ 配置参数

### 核心参数（在 `Square_Config.h` 中）
```c
#define SQUARE_STRAIGHT_SPEED       3000    // 直线行驶速度
#define SQUARE_TURN_SPEED           2000    // 转弯时单轮转动速度
#define SQUARE_MIN_SPEED            1000    // 最小速度（丢线时使用）
```

### 传感器布局
```
[0][1][2][3][4][5][6][7]
 左侧    中心    右侧
```

- 检测到左侧传感器(0,1,2)：执行左转
- 检测到右侧传感器(5,6,7)：执行右转
- 检测到中心传感器(3,4)：直线行驶

## 🔧 使用方法

### 1. 基础使用（最简单）
```c
#include "Square_Track_Simple.h"

int main(void)
{
    // 初始化硬件...
    
    Square_Track_Init();  // 初始化正方形轨道系统
    
    while(1) {
        // 获取传感器数据
        digital_data = Get_Digtal_For_User(&sensor);
        
        // 执行控制（就这一行！）
        Square_Track_Control(digital_data);
        
        delay_ms(10);
    }
}
```

### 2. 速度调整
```c
// 如果转弯太快，降低速度
square_ctrl.turn_speed = 1500;

// 如果直线太慢，提高速度  
square_ctrl.straight_speed = 3500;
```

## 🎛️ 调试指南

### 问题1：转弯半径太大，冲出轨道
**解决方案：**
- 降低转弯速度：`SQUARE_TURN_SPEED = 1500`
- 确保一轮完全停止（PWM = 0）

### 问题2：转弯太慢，卡在角落
**解决方案：**
- 提高转弯速度：`SQUARE_TURN_SPEED = 2500`
- 检查电机是否有阻力

### 问题3：直线行驶不稳定
**解决方案：**
- 调整直线速度：`SQUARE_STRAIGHT_SPEED = 2500`
- 检查传感器是否正常工作

### 问题4：检测不到转弯
**解决方案：**
- 检查传感器阈值设置
- 确认传感器布局正确

## 📊 性能对比

| 项目 | 复杂系统 | 简化系统 | 优势 |
|------|----------|----------|------|
| 代码行数 | 1000+ | <200 | **简单5倍** |
| 配置参数 | 50+ | 3个 | **易调试** |
| 转弯策略 | 差速控制 | 一轮停止 | **转弯精确** |
| 调试难度 | 复杂 | 简单 | **易理解** |

## 🔍 工作原理

### 传感器检测逻辑
```c
// 检测左转：左侧传感器检测到黑线
if(!((digital_data >> 0) & 0x01) || !((digital_data >> 1) & 0x01) || !((digital_data >> 2) & 0x01)) {
    return SQUARE_STATE_TURN_LEFT;
}

// 检测右转：右侧传感器检测到黑线  
if(!((digital_data >> 5) & 0x01) || !((digital_data >> 6) & 0x01) || !((digital_data >> 7) & 0x01)) {
    return SQUARE_STATE_TURN_RIGHT;
}
```

### 转弯执行逻辑
- **左转时**：右轮以`SQUARE_TURN_SPEED`转动，左轮停止(PWM=0)
- **右转时**：左轮以`SQUARE_TURN_SPEED`转动，右轮停止(PWM=0)
- **直行时**：两轮都以`SQUARE_STRAIGHT_SPEED`转动

## ✅ 优势总结

1. **极简设计** - 代码量少，易于理解
2. **专用优化** - 专门针对正方形轨道的90度转弯
3. **转弯精确** - 一轮停止策略实现最小转弯半径
4. **易于调试** - 只需调整3个核心参数
5. **高效执行** - 无复杂计算，响应快速
6. **完全可控** - 每个动作都是确定的，可预测的

## 🎉 总结

这个简化版本完全满足您的需求：
- ✅ 专门针对正方形轨道
- ✅ 一轮停止，一轮转动的转弯策略  
- ✅ 系统极度简化，无多余复杂性
- ✅ 易于理解和调试
- ✅ 高效可靠的90度直角转弯

只需要调整3个参数就能完美适配您的正方形轨道！

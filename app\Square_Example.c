/**
 * @file Square_Example.c
 * @brief 正方形轨道简化版使用示例
 * @details 专门针对正方形轨道的90度直角转弯，一轮停止一轮转动
 */

#include "Square_Track_Simple.h"
#include "bsp_system.h"

/**
 * @brief 基础使用示例
 * @param sensor 传感器对象指针
 */
void Square_Basic_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    
    // 1. 初始化正方形轨道系统
    Square_Track_Init();
    
    // 2. 主循环 - 就这么简单！
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        
        // 执行正方形轨道控制
        Square_Track_Control(digital_data);
        
        // 可选：显示调试信息
        #if SQUARE_DEBUG_ENABLE
        Square_Debug_Info();
        #endif
        
        delay_ms(10);  // 控制循环频率
    }
}

/**
 * @brief 带速度调整的示例
 * @param sensor 传感器对象指针
 */
void Square_Speed_Adjust_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    
    Square_Track_Init();
    
    // 可以动态调整速度
    square_ctrl.straight_speed = 2500;  // 降低直线速度
    square_ctrl.turn_speed = 1800;      // 降低转弯速度
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        
        Square_Track_Control(digital_data);
        
        // 可以根据需要实时调整速度
        // 例如：如果转弯太快，可以降低turn_speed
        // 如果转弯太慢，可以增加turn_speed
        
        delay_ms(10);
    }
}

/**
 * @brief 带按键控制的示例
 * @param sensor 传感器对象指针
 */
void Square_Key_Control_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    static int speed_level = 1;  // 速度等级：0=慢，1=中，2=快
    
    Square_Track_Init();
    
    while(1) {
        // 检查按键（假设有按键切换速度）
        if(/* 按键按下条件 */ 0) {
            speed_level = (speed_level + 1) % 3;
            
            switch(speed_level) {
                case 0:  // 慢速
                    square_ctrl.straight_speed = 2000;
                    square_ctrl.turn_speed = 1500;
                    // OLED_ShowString(0, 0, "Speed: Slow", 12, 1);
                    break;
                case 1:  // 中速
                    square_ctrl.straight_speed = 3000;
                    square_ctrl.turn_speed = 2000;
                    // OLED_ShowString(0, 0, "Speed: Mid", 12, 1);
                    break;
                case 2:  // 快速
                    square_ctrl.straight_speed = 3500;
                    square_ctrl.turn_speed = 2500;
                    // OLED_ShowString(0, 0, "Speed: Fast", 12, 1);
                    break;
            }
            delay_ms(200);  // 防抖
        }
        
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Square_Track_Control(digital_data);
        
        delay_ms(10);
    }
}

/**
 * @brief 调试模式示例
 * @param sensor 传感器对象指针
 */
void Square_Debug_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    static int debug_counter = 0;
    
    Square_Track_Init();
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        
        Square_Track_Control(digital_data);
        
        // 每100次循环显示一次状态
        debug_counter++;
        if(debug_counter >= 100) {
            debug_counter = 0;
            
            // 显示当前状态
            switch(square_ctrl.state) {
                case SQUARE_STATE_STRAIGHT:
                    // OLED_ShowString(0, 0, "State: Straight", 12, 1);
                    break;
                case SQUARE_STATE_TURN_LEFT:
                    // OLED_ShowString(0, 0, "State: Turn L", 12, 1);
                    break;
                case SQUARE_STATE_TURN_RIGHT:
                    // OLED_ShowString(0, 0, "State: Turn R", 12, 1);
                    break;
                case SQUARE_STATE_LOST:
                    // OLED_ShowString(0, 0, "State: Lost", 12, 1);
                    break;
            }
            
            // 显示速度信息
            // OLED_ShowNum(0, 12, square_ctrl.straight_speed, 4, 12, 1);
            // OLED_ShowNum(0, 24, square_ctrl.turn_speed, 4, 12, 1);
            // OLED_Refresh();
        }
        
        delay_ms(10);
    }
}

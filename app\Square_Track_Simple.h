#ifndef _SQUARE_TRACK_SIMPLE_H
#define _SQUARE_TRACK_SIMPLE_H

#include "motor.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "Square_Config.h"  // 引入配置文件

// 轨道状态枚举（简化）
typedef enum {
    SQUARE_STATE_STRAIGHT = 0,    // 直线行驶
    SQUARE_STATE_TURN_LEFT,       // 左转（右轮转，左轮停）
    SQUARE_STATE_TURN_RIGHT,      // 右转（左轮转，右轮停）
    SQUARE_STATE_LOST             // 丢线
} Square_State_t;

// 简化的控制结构
typedef struct {
    Square_State_t state;         // 当前状态
    int straight_speed;           // 直线速度
    int turn_speed;               // 转弯速度
    int lost_count;               // 丢线计数
} Square_Control_t;

// 全局控制变量
extern Square_Control_t square_ctrl;

// 函数声明
void Square_Track_Init(void);
void Square_Track_Control(unsigned char digital_data);
Square_State_t Square_Analyze_State(unsigned char digital_data);
void Square_Go_Straight(void);
void Square_Turn_Left(void);
void Square_Turn_Right(void);
void Square_Handle_Lost(void);
void Square_Stop(void);

// 调试函数
void Square_Debug_Info(void);

#endif

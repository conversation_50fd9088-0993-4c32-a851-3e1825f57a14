#ifndef _SQUARE_CONFIG_H
#define _SQUARE_CONFIG_H

/**
 * @file Square_Config.h
 * @brief 正方形轨道专用配置文件（简化版）
 * @details 专门针对正方形轨道的90度直角转弯优化
 */

/*************************** 基础速度配置 ***************************/
// 根据您的需求，这些参数可以轻松调整
#define SQUARE_STRAIGHT_SPEED       3000    // 直线行驶速度
#define SQUARE_TURN_SPEED           2000    // 转弯时单轮转动速度
#define SQUARE_MIN_SPEED            1000    // 最小速度（丢线时使用）
#define SQUARE_MAX_SPEED            4000    // 最大速度限制

/*************************** 传感器配置 ***************************/
#define SQUARE_SENSOR_COUNT         8       // 传感器数量
#define SQUARE_LEFT_SENSORS         3       // 左侧传感器数量（0,1,2）
#define SQUARE_RIGHT_SENSORS        3       // 右侧传感器数量（5,6,7）
#define SQUARE_CENTER_SENSORS       2       // 中心传感器数量（3,4）

/*************************** 状态检测配置 ***************************/
#define SQUARE_LOST_COUNT_MAX       5       // 丢线计数阈值
#define SQUARE_TURN_DETECT_TIME     50      // 转弯检测时间(ms)

/*************************** 调试配置 ***************************/
#define SQUARE_DEBUG_ENABLE         1       // 启用调试功能
#define SQUARE_DEBUG_OLED           1       // 启用OLED调试显示

/*************************** 安全配置 ***************************/
#define SQUARE_EMERGENCY_STOP       1       // 启用紧急停止
#define SQUARE_SPEED_LIMIT_CHECK    1       // 启用速度限制检查

/*************************** 参数验证 ***************************/
// 编译时检查参数合理性
#if SQUARE_STRAIGHT_SPEED > SQUARE_MAX_SPEED
    #error "Straight speed cannot exceed max speed"
#endif

#if SQUARE_TURN_SPEED > SQUARE_MAX_SPEED
    #error "Turn speed cannot exceed max speed"
#endif

#if SQUARE_MIN_SPEED >= SQUARE_STRAIGHT_SPEED
    #error "Min speed should be less than straight speed"
#endif

/*************************** 使用说明 ***************************/
/*
使用方法：

1. 在main函数中初始化：
   Square_Track_Init();

2. 在主循环中调用：
   Square_Track_Control(digital_sensor_data);

3. 调整参数：
   - 如果直线速度太快/太慢：调整 SQUARE_STRAIGHT_SPEED
   - 如果转弯速度太快/太慢：调整 SQUARE_TURN_SPEED
   - 如果转弯不够灵敏：增大 SQUARE_TURN_SPEED
   - 如果转弯过度：减小 SQUARE_TURN_SPEED

4. 转弯原理：
   - 左转：右轮转动(SQUARE_TURN_SPEED)，左轮停止(0)
   - 右转：左轮转动(SQUARE_TURN_SPEED)，右轮停止(0)
   - 这样实现最小转弯半径，适合90度直角转弯

5. 传感器布局：
   [0][1][2][3][4][5][6][7]
    左侧    中心    右侧
   - 检测到左侧传感器(0,1,2)：执行左转
   - 检测到右侧传感器(5,6,7)：执行右转
   - 检测到中心传感器(3,4)：直线行驶
*/

#endif /* _SQUARE_CONFIG_H */
